---
import { getCompleteMenu } from '../utils/menuParser.ts';

// Get the menu data - this will read menu.md and process @include: directives
const menuData = getCompleteMenu();

// Function to extract the main title from markdown content
function extractTitle(markdown: string): string {
  const match = markdown.match(/^# (.+)$/m);
  return match ? match[1] : 'Untitled Product';
}

// Function to create a URL-friendly slug from a title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

// Function to extract images from markdown
function extractImages(markdown: string): string[] {
  const imageRegex = /!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)(?:\s*\{(\d+)(?::(\d+))?\})?/g;
  const images: string[] = [];
  let match;

  while ((match = imageRegex.exec(markdown)) !== null) {
    const [fullMatch, alt, src, title, width, height] = match;
    let imgTag = `<img src="${src}" alt="${alt}"`;
    if (title && title.trim()) {
      imgTag += ` title="${title}"`;
    }
    if (width) {
      if (height) {
        imgTag += ` style="width: ${width}px; height: ${height}px;"`;
      } else {
        imgTag += ` style="width: ${width}px; height: auto;"`;
      }
    }
    imgTag += ' />';
    images.push(imgTag);
  }

  return images;
}

// Function to process callouts in markdown
function processCallouts(markdown: string): string {
  const lines = markdown.split('\n');
  const result: string[] = [];
  let inCallout = false;
  let calloutType = '';
  let calloutTitle = '';
  let calloutContent: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Check for callout start
    const calloutMatch = line.match(/^> \[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION|INFO|SUCCESS|ERROR|DANGER)\]\s*(.*?)$/);
    if (calloutMatch) {
      // If we were already in a callout, close it first
      if (inCallout) {
        result.push(`<div class="callout callout-${calloutType}"><div class="callout-title"><span class="callout-icon">${getCalloutIcon(calloutType)}</span>${calloutTitle}</div><div class="callout-content">${calloutContent.join('<br>')}</div></div>`);
        calloutContent = [];
      }

      inCallout = true;
      calloutType = calloutMatch[1].toLowerCase();
      calloutTitle = calloutMatch[2] || calloutMatch[1];
    }
    // Check for callout content line
    else if (inCallout && line.startsWith('> ')) {
      calloutContent.push(line.substring(2));
    }
    // End of callout or regular line
    else {
      if (inCallout) {
        result.push(`<div class="callout callout-${calloutType}"><div class="callout-title"><span class="callout-icon">${getCalloutIcon(calloutType)}</span>${calloutTitle}</div><div class="callout-content">${calloutContent.join('<br>')}</div></div>`);
        inCallout = false;
        calloutContent = [];
      }
      result.push(line);
    }
  }

  // Close any remaining callout
  if (inCallout) {
    result.push(`<div class="callout callout-${calloutType}"><div class="callout-title"><span class="callout-icon">${getCalloutIcon(calloutType)}</span>${calloutTitle}</div><div class="callout-content">${calloutContent.join('<br>')}</div></div>`);
  }

  return result.join('\n');
}

// Function to convert markdown to HTML with ID support (images removed)
function markdownToHtml(markdown: string, productId?: string): string {
  // Process callouts first
  let processedMarkdown = processCallouts(markdown);

  let html = processedMarkdown
    // Remove images first (they'll be handled separately)
    .replace(/!\[([^\]]*)\]\(([^)]+)(?:\s+"([^"]*)")?\)(?:\s*\{(\d+)(?::(\d+))?\})?/g, '')
    // Headers with ID for main title
    .replace(/^# (.*$)/gm, (_, title) => {
      if (productId) {
        return `<h1 id="${productId}">${title}</h1>`;
      }
      return `<h1>${title}</h1>`;
    })
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    // Auto-link URLs (must come after manual links to avoid conflicts)
    .replace(/(^|[^"'>])(https?:\/\/[^\s<>"']+)/g, '$1<a href="$2" target="_blank" rel="noopener noreferrer">$2</a>')
    // Lists
    .replace(/^- (.*$)/gm, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    // Paragraphs (skip HTML elements and empty lines)
    .replace(/^(?!<|^\s*$)(.*$)/gm, '<p>$1</p>')
    // Clean up empty paragraphs
    .replace(/<p><\/p>/g, '')
    .replace(/<p>\s*<\/p>/g, '');

  return html;
}

// Function to get callout icons
function getCalloutIcon(type: string): string {
  const icons = {
    note: '📝',
    tip: '💡',
    important: '❗',
    warning: '⚠️',
    caution: '⚠️',
    info: 'ℹ️',
    success: '✅',
    error: '❌',
    danger: '🚨'
  };
  return icons[type] || '📝';
}

// Generate TOC data from all products in all categories
const allProducts = menuData.sections
  .filter(section => section.type === 'category')
  .flatMap(section => section.products || []);

const tocItems = allProducts.map((product) => {
  const title = extractTitle(product.content);
  const slug = createSlug(title);
  return {
    title,
    slug,
    category: product.category,
    categoryIndex: product.categoryIndex
  };
});

// Group TOC items by category
const tocSections = tocItems.reduce((acc, item) => {
  if (!acc[item.category]) {
    acc[item.category] = [];
  }
  acc[item.category].push(item);
  return acc;
}, {} as Record<string, typeof tocItems>);

// Sort categories by categoryIndex
const sortedTocSections = Object.keys(tocSections)
  .sort((a, b) => {
    const aIndex = tocSections[a][0]?.categoryIndex || 0;
    const bIndex = tocSections[b][0]?.categoryIndex || 0;
    return aIndex - bIndex;
  })
  .reduce((acc, key) => {
    acc[key] = tocSections[key];
    return acc;
  }, {} as Record<string, typeof tocItems>);
---

<div class="menu-container">
  <!-- Render content sections first (main heading and intro text) -->
  <div class="menu-content">
    {menuData.sections.map((section) => (
      section.type === 'content' && (
        <div class="content-section">
          <div set:html={markdownToHtml(section.content || '')}></div>
        </div>
      )
    ))}
  </div>

  <!-- Table of Contents (after intro, before categories) -->
  {tocItems.length > 0 && (
    <div class="toc-section">
      <h2>📋 Table of Contents</h2>
      <nav class="toc-nav">
        <div class="toc-columns">
          {Object.entries(sortedTocSections).map(([sectionName, items], sectionIndex) => {
            // Skip the stacked categories here, we'll handle them separately
            if (sectionName === '🎢 Stimulants' || sectionName === '🤭 Downers') {
              return null;
            }

            return (
              <div class="toc-column">
                <div class="toc-section-group">
                  <h3 class="toc-section-title">{sectionIndex + 1}. {sectionName}</h3>
                  <ul class="toc-list">
                    {items.map((item, itemIndex) => (
                      <li class="toc-item">
                        <a href={`#${item.slug}`} class="toc-link">
                          {sectionIndex + 1}.{itemIndex + 1} {item.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}

          <!-- Stacked categories column -->
          <div class="toc-column toc-stacked-column">
            {Object.entries(sortedTocSections).map(([sectionName, items], sectionIndex) => {
              // Only render the stacked categories
              if (sectionName !== '🎢 Stimulants' && sectionName !== '🤭 Downers') {
                return null;
              }

              return (
                <div class="toc-section-group">
                  <h3 class="toc-section-title">{sectionIndex + 1}. {sectionName}</h3>
                  <ul class="toc-list">
                    {items.map((item, itemIndex) => (
                      <li class="toc-item">
                        <a href={`#${item.slug}`} class="toc-link">
                          {sectionIndex + 1}.{itemIndex + 1} {item.title}
                        </a>
                      </li>
                    ))}
                  </ul>
                </div>
              );
            })}
          </div>
        </div>
      </nav>
    </div>
  )}

  <!-- Render category sections -->
  <div class="categories-content">
    {menuData.sections.map((section) => (
      section.type === 'category' && (
        <div class="category-section" data-category-index={section.categoryIndex} data-category={section.categoryName}>
          <h2 class="category-header">{section.categoryName}</h2>
          <div class="category-products">
            {section.products?.map((product, productIndex) => {
              const productSlug = createSlug(extractTitle(product.content));
              const productNumber = `${section.categoryIndex}.${productIndex + 1}`;
              const productTitle = extractTitle(product.content);
              const images = extractImages(product.content);
              const contentWithoutImages = markdownToHtml(product.content, productSlug);

              return (
                <div class="product-item" data-product-number={productNumber}>
                  <div class="product-title">
                    <h1 id={productSlug}>{productTitle}</h1>
                  </div>
                  <div class="product-columns">
                    <div class="product-content">
                      <div set:html={contentWithoutImages.replace(/<h1[^>]*>.*?<\/h1>/g, '')}></div>
                    </div>
                    <div class="product-images">
                      {images.map((img) => (
                        <div set:html={img}></div>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )
    ))}
  </div>
</div>

<style>
  .menu-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: system-ui, sans-serif;
  }

  .menu-content {
    margin-bottom: 2rem;
  }

  .categories-content {
    margin-top: 1rem;
  }

  .content-section {
    margin-bottom: 2rem;
  }

  .content-section h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
  }

  /* Category sections */
  .category-section {
    margin-bottom: 3rem;
  }

  .category-header {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 2rem;
    padding-bottom: 0.75rem;
    border-bottom: 3px solid #3498db;
  }

  .category-products {
    display: flex;
    flex-direction: column;
    gap: 2rem;
  }

  /* Table of Contents Styles */
  .toc-section {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 3rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .toc-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-size: 1.4rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }

  .toc-nav {
    max-width: 100%;
  }

  /* Multi-column layout for TOC */
  .toc-columns {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 2rem;
    align-items: start;
  }

  .toc-column {
    min-width: 0; /* Prevents overflow issues */
  }

  /* Special handling for stacked categories column */
  .toc-stacked-column {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .toc-section-group {
    margin-bottom: 0;
  }

  .toc-section-title {
    color: #34495e;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
  }

  .toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .toc-item {
    margin: 0;
  }

  .toc-link {
    display: block;
    padding: 0.75rem 1rem;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    color: #2c3e50;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .toc-link:hover {
    background: #3498db;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
  }

  .toc-link:active {
    transform: translateY(0);
  }

  /* Category sections are now handled above */

  .product-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
  }

  .product-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .product-title {
    margin-bottom: 1.5rem;
  }

  .product-title h1 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin: 0;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
    position: relative;
  }

  .product-item h3 {
    color: #2c3e50;
    margin: 1.5rem 0 0.5rem 0;
    font-size: 1.2rem;
  }

  .product-columns {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
  }

  .product-content {
    min-width: 0; /* Prevents overflow issues */
  }

  .product-images {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  /* Callout Styles - Global to work with dynamically generated content */
  :global(.callout) {
    border-radius: 8px;
    margin: 1rem 0;
    padding: 1rem;
    border-left: 4px solid;
    background: #f8f9fa;
  }

  /* Callouts in product content should be 60% of column width */
  :global(.product-content .callout) {
    width: 60%;
    max-width: 60%;
    box-sizing: border-box;
  }

  /* Callouts in shop information sections should be wider */
  :global(.category-section[data-category="ℹ️ Shop Information"] .callout) {
    width: 85%;
    max-width: 85%;
    box-sizing: border-box;
  }

  :global(.callout-title) {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  :global(.callout-icon) {
    font-size: 1.1em;
  }

  :global(.callout-content) {
    margin: 0;
  }

  /* Callout type-specific colors */
  :global(.callout-note) {
    border-left-color: #3498db;
    background: #e3f2fd;
  }

  :global(.callout-tip) {
    border-left-color: #2ecc71;
    background: #e8f5e8;
  }

  :global(.callout-important) {
    border-left-color: #9b59b6;
    background: #f3e5f5;
  }

  :global(.callout-warning), :global(.callout-caution) {
    border-left-color: #f39c12;
    background: #fff3cd;
  }

  :global(.callout-info) {
    border-left-color: #17a2b8;
    background: #d1ecf1;
  }

  :global(.callout-success) {
    border-left-color: #28a745;
    background: #d4edda;
  }

  :global(.callout-error), :global(.callout-danger) {
    border-left-color: #dc3545;
    background: #f8d7da;
  }

  /* Product numbering is handled by JavaScript */

  .product-item h2 {
    color: #34495e;
    font-size: 1.2rem;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  /* List styling - make global to override browser defaults */
  :global(.product-item ul) {
    margin: 0.5rem 0 1rem 0;
    padding-left: 0 !important;
    list-style: none !important;
  }

  :global(.product-item li) {
    margin-bottom: 0;
    line-height: 1.6;
    padding-left: 0 !important;
    margin-left: 0 !important;
  }

  .product-item strong {
    color: #2c3e50;
  }

  /* Link styling - global to override browser defaults */
  :global(.product-item a) {
    color: #3498db !important;
    text-decoration: none;
    font-weight: 500;
  }

  :global(.product-item a:hover) {
    text-decoration: underline;
    color: #2980b9 !important;
  }

  .product-item p {
    line-height: 1.6;
    margin-bottom: 1rem;
    color: #555;
  }

  /* Remove margin/padding above first paragraph in product content - global to override defaults */
  :global(.product-content > div > p:first-child) {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }

  .product-images img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: block;
  }

  /* Images with custom dimensions should respect those dimensions but still be responsive */
  .product-images img[style*="width"] {
    max-width: 100%;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Add scroll margin to account for any fixed headers */
  .product-item h1[id] {
    scroll-margin-top: 2rem;
  }

  /* Responsive design */
  @media (max-width: 1024px) {
    /* Tablet layout - 2 columns */
    .toc-columns {
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
    }
  }

  @media (max-width: 768px) {
    .menu-container {
      padding: 1rem;
    }

    .product-item {
      padding: 1.5rem;
    }

    .product-columns {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .product-images {
      order: -1; /* Move images above content on mobile */
    }

    .toc-section {
      padding: 1.5rem;
      margin-bottom: 2rem;
    }

    /* Stack columns vertically on mobile */
    .toc-columns {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .toc-stacked-column {
      gap: 1rem;
    }

    .toc-link {
      padding: 0.75rem 1rem;
      font-size: 0.9rem;
    }
  }
</style>

<script>
  // Add category and product numbering
  document.addEventListener('DOMContentLoaded', function() {
    // Add category numbering to category headers
    const categoryHeaders = document.querySelectorAll('.category-header');
    categoryHeaders.forEach((header, index) => {
      const categoryNumber = index + 1;
      header.textContent = `${categoryNumber}. ${header.textContent}`;
    });

    // Add product numbering to product items
    const productItems = document.querySelectorAll('.product-item[data-product-number]');
    productItems.forEach((productItem) => {
      const productNumber = productItem.getAttribute('data-product-number');
      const titleH1 = productItem.querySelector('.product-title h1');
      if (titleH1 && productNumber) {
        titleH1.textContent = `${productNumber} ${titleH1.textContent}`;
      }
    });
  });
</script>
